/* Learning Platform Styles */

.learning-container {
  font-family: 'Inter', sans-serif;
  background-color: #f8fafc; /* slate-50 */
  min-height: 100vh;
  color: #1e293b; /* slate-800 */
}

/* Navigation */
.learning-nav {
  background-color: white;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  border-bottom: 1px solid #e2e8f0; /* slate-200 */
  position: sticky;
  top: 0;
  z-index: 10;
}

.learning-nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.learning-nav-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2563eb; /* blue-600 */
}

.learning-nav-logout {
  background-color: #475569; /* slate-600 */
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 600;
  transition: background-color 0.2s ease;
}

.learning-nav-logout:hover {
  background-color: #334155; /* slate-700 */
}

/* Main Content */
.learning-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

@media (min-width: 768px) {
  .learning-main {
    padding: 2rem;
  }
}

/* Header */
.learning-header {
  margin-bottom: 2rem;
}

.learning-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827; /* gray-900 */
  margin-bottom: 0.25rem;
}

.learning-subtitle {
  color: #6b7280; /* gray-500 */
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
  grid-template-areas:
    "main"
    "sidebar";
  margin-bottom: 3rem;
}

@media (min-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: repeat(3, 1fr);
    grid-template-areas: "main main sidebar";
  }
}

.main-column {
  grid-area: main;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.sidebar-column {
  grid-area: sidebar;
  display: flex;
  flex-direction: column;
}

/* Cards */
.learning-card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  padding: 1.5rem;
  border: 1px solid #f1f5f9; /* slate-100 */
}

/* Main Gradient */
.main-gradient {
  background: linear-gradient(135deg, #0079FF, #004AAD);
}

/* Recommendation Card */
.recommendation-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: #dbeafe; /* blue-50 */
  border-radius: 0.5rem;
  border: 1px solid #bfdbfe; /* blue-200 */
}

.recommendation-icon {
  font-size: 1.875rem;
  margin-right: 1rem;
}

.recommendation-content {
  flex-grow: 1;
}

.recommendation-title {
  font-weight: 600;
  color: #1e40af; /* blue-800 */
  margin-bottom: 0.25rem;
}

.recommendation-description {
  font-size: 0.875rem;
  color: #64748b; /* slate-600 */
}

.recommendation-button {
  margin-left: 1rem;
  background-color: #2563eb; /* blue-600 */
  color: white;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  flex-shrink: 0;
  transition: background-color 0.2s ease;
}

.recommendation-button:hover {
  background-color: #1d4ed8; /* blue-700 */
}

/* Chart Container */
.chart-container {
  position: relative;
  width: 100%;
  height: 300px;
}

/* User Score Card */
.user-score-header {
  color: white;
  border-radius: 0.75rem 0.75rem 0 0;
  padding: 1.5rem;
  text-align: center;
}

.user-score-label {
  color: #bfdbfe; /* blue-200 */
  font-size: 1.125rem;
}

.user-score-value {
  font-size: 3.75rem;
  font-weight: 900;
  margin: 0.5rem 0;
}

.user-score-percentage {
  font-size: 1.875rem;
  font-weight: 700;
}

.user-score-improvement {
  color: #bfdbfe; /* blue-200 */
  font-size: 0.875rem;
}

.user-stats-content {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.user-stats-title {
  font-weight: 700;
  font-size: 1.125rem;
  text-align: center;
  margin-bottom: 1rem;
}

.user-avatar {
  margin: 0 auto 1rem;
}

.user-avatar img {
  width: 6rem;
  height: 6rem;
  border-radius: 50%;
  border: 4px solid white;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.user-stats-list {
  margin-top: 0.5rem;
  border-top: 1px solid #e2e8f0;
  padding-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.user-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f1f5f9; /* slate-100 */
  padding: 0.5rem;
  border-radius: 0.375rem;
}

.user-stat-label {
  font-size: 0.875rem;
  color: #64748b; /* slate-600 */
}

.user-stat-value {
  font-weight: 700;
  color: #1e293b; /* slate-800 */
}

/* Categories Grid */
.categories-section {
  margin-top: 3rem;
}

.categories-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.category-grid {
  display: grid;
  gap: 0.75rem;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
}

.category-card {
  display: block;
  background-color: white;
  padding: 1.5rem;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0; /* slate-200 */
  text-decoration: none;
  color: inherit;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.category-icon {
  font-size: 1.875rem;
  margin-bottom: 0.75rem;
  display: block;
}

.category-title {
  font-weight: 700;
  font-size: 1.125rem;
  margin-bottom: 0.25rem;
}

.category-description {
  font-size: 0.875rem;
  color: #6b7280; /* gray-500 */
}

/* Card Titles */
.card-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
}
