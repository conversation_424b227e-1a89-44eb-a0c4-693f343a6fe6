import '../styles/learning.css';

class LearningView {
  constructor(data) {
    this.data = data;
    this.chartInstance = null;
  }

  render() {
    const container = document.createElement('div');
    container.className = 'learning-container';
    container.innerHTML = this.getHTML();

    // Load Inter font if not already loaded
    this.loadFont();

    // Load Chart.js
    this.loadChartJS().then(() => {
      this.initChart();
    });

    return container;
  }

  loadFont() {
    // Load Inter font if not already loaded
    if (!document.querySelector('link[href*="fonts.googleapis.com"]')) {
      const fontLink = document.createElement('link');
      fontLink.rel = 'stylesheet';
      fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&display=swap';
      document.head.appendChild(fontLink);
    }
  }

  loadChartJS() {
    return new Promise((resolve) => {
      if (window.Chart) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
      script.onload = resolve;
      document.head.appendChild(script);
    });
  }

  getHTML() {
    const { userData, uiText, mainRecommendation, categories } = this.data;
    
    return `
      <!-- Navigation -->
      <nav class="learning-nav">
        <div class="learning-nav-content">
          <div class="learning-nav-title">${uiText.appTitle}</div>
          <div>
            <a href="#" class="learning-nav-logout">${uiText.logoutButton}</a>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <main class="learning-main">
        <!-- Header -->
        <div class="learning-header">
          <h1 class="learning-title">${uiText.welcomeTitle}</h1>
          <p class="learning-subtitle">${uiText.welcomeSubtitle}</p>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
          <!-- Main Column -->
          <div class="main-column">
            <!-- Recommendation Card -->
            <div class="learning-card">
              <h2 class="card-title">${uiText.mainRecommendationTitle}</h2>
              <div class="recommendation-item">
                <span class="recommendation-icon">${mainRecommendation.icon}</span>
                <div class="recommendation-content">
                  <p class="recommendation-title">${mainRecommendation.title}</p>
                  <p class="recommendation-description">${mainRecommendation.description}</p>
                </div>
                <button class="recommendation-button">${mainRecommendation.buttonText}</button>
              </div>
            </div>

            <!-- Progress Chart Card -->
            <div class="learning-card">
              <h2 class="card-title">${uiText.progressChartTitle}</h2>
              <div class="chart-container">
                <canvas id="progressChart"></canvas>
              </div>
            </div>
          </div>

          <!-- Sidebar Column -->
          <div class="sidebar-column">
            <!-- User Score Card -->
            <div class="learning-card" style="padding: 0; display: flex; flex-direction: column; height: 100%;">
              <div class="user-score-header main-gradient">
                <p class="user-score-label">${uiText.userScoreTitle}</p>
                <p class="user-score-value">${userData.accentScore}<span class="user-score-percentage">%</span></p>
                <p class="user-score-improvement">${uiText.scoreImprovement}</p>
              </div>
              <div class="user-stats-content">
                <h3 class="user-stats-title">${uiText.userStatsTitle}</h3>
                <div class="user-avatar">
                  <img src="${userData.profileImage}" alt="Foto Profil ${userData.name}">
                </div>
                <div class="user-stats-list">
                  <div class="user-stat-item">
                    <span class="user-stat-label">${uiText.statLabels.completedExercises}</span>
                    <span class="user-stat-value">${userData.stats.completedExercises}</span>
                  </div>
                  <div class="user-stat-item">
                    <span class="user-stat-label">${uiText.statLabels.practiceTime}</span>
                    <span class="user-stat-value">${userData.stats.practiceTime}</span>
                  </div>
                  <div class="user-stat-item">
                    <span class="user-stat-label">${uiText.statLabels.categoriesTried}</span>
                    <span class="user-stat-value">${userData.stats.categoriesTried}</span>
                  </div>
                  <div class="user-stat-item">
                    <span class="user-stat-label">${uiText.statLabels.categoriesMastered}</span>
                    <span class="user-stat-value">${userData.stats.categoriesMastered}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Categories Section -->
        <div class="categories-section">
          <h2 class="categories-title">${uiText.categoriesTitle}</h2>
          <div class="category-grid">
            ${categories.map(category => `
              <a href="${category.href}" class="category-card">
                <div class="category-icon">${category.icon}</div>
                <h3 class="category-title">${category.title}</h3>
                <p class="category-description">${category.description}</p>
              </a>
            `).join('')}
          </div>
        </div>
      </main>
    `;
  }

  initChart() {
    const canvas = document.getElementById('progressChart');
    if (!canvas || !window.Chart) return;

    const ctx = canvas.getContext('2d');
    const chartConfig = this.data.chartConfig;

    this.chartInstance = new Chart(ctx, chartConfig);
  }

  destroy() {
    if (this.chartInstance) {
      this.chartInstance.destroy();
      this.chartInstance = null;
    }
  }
}

export default LearningView;
