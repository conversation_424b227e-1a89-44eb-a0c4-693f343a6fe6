class AppModel {
  constructor() {
    this.currentPage = 'welcome';
    this.isRecording = false;
    // Array of test texts for random selection
    this.testTexts = [
      'I went to the store this afternoon to buy groceries.',
      'It was really busy, and the line was long.',
      'I still found everything I needed.',
      'The line moved faster than I expected.',
      'I felt glad to get it all done and head home.',
      'While waiting for the bus, I saw a cat chasing a butterfly.',
      'You can try restarting it, but I’m not sure it’ll work.',
      'Honestly, I didn’t expect them to arrive so early.',
      'She said she would call, but I haven’t heard from her yet.'
    ];

    // UI Text Content (Indonesian)
    this.welcomeText = 'Mau tes seberapa bagus kemampuan berbicara bahasa Inggris kamu?';
    this.tryAgainButtonText = 'Coba Lagi';

    this.confidenceDescriptions = {
      excellent: "Keren sekali! Aksen Amerika kamu sangat jelas dan percaya diri.",
      great: "Bagus sekali! Aksen kamu sudah sangat baik, hanya butuh sedikit penyempurnaan.",
      good: "<PERSON><PERSON><PERSON> baik! Kamu sudah memiliki dasar aksen yang oke, tinggal terus dilatih.",
      notBad: "<PERSON><PERSON><PERSON>! Masih perlu banyak latihan, tapi kamu sudah berada di jalur yang tepat.",
      progress: "Sudah berkembang! Coba perhatikan lebih teliti lagi cara pengucapan dan intonasi bicaramu.",
      keepPracticing: "Terus berlatih! Semua yang ahli pernah mulai dari nol, dan kamu bisa jadi salah-satunya kamu."
    };
  }

  setCurrentPage(page) {
    this.currentPage = page;
  }

  getCurrentPage() {
    return this.currentPage;
  }

  setRecording(status) {
    this.isRecording = status;
  }

  isCurrentlyRecording() {
    return this.isRecording;
  }

  getTestText() {
    // Return a random test text from the array
    const randomIndex = Math.floor(Math.random() * this.testTexts.length);
    return this.testTexts[randomIndex];
  }

  setLastResult(result) {
    this.lastResult = result;
  }

  getLastResult() {
    return this.lastResult;
  }

  getWelcomeText() {
    return this.welcomeText;
  }

  getTryAgainButtonText() {
    return this.tryAgainButtonText;
  }

  getConfidenceDescription(confidence) {
    if (confidence >= 90) {
      return this.confidenceDescriptions.excellent;
    } else if (confidence >= 80) {
      return this.confidenceDescriptions.great;
    } else if (confidence >= 70) {
      return this.confidenceDescriptions.good;
    } else if (confidence >= 60) {
      return this.confidenceDescriptions.notBad;
    } else if (confidence >= 50) {
      return this.confidenceDescriptions.progress;
    } else {
      return this.confidenceDescriptions.keepPracticing;
    }
  }

  formatConfidenceText(confidence) {
    return `Tingkat kepercayaan aksen Amerika kamu: ${confidence.toFixed(1)}%`;
  }
}

export default AppModel;
