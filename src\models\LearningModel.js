class LearningModel {
  constructor() {
    // User data
    this.userData = {
      name: '<PERSON><PERSON>',
      accentScore: 82,
      scoreImprovement: 2,
      profileImage: 'https://placehold.co/100x100/E2E8F0/475569?text=B',
      stats: {
        completedExercises: 128,
        practiceTime: '14 Jam',
        categoriesTried: '5/7',
        categoriesMastered: 2
      }
    };

    // Progress chart data
    this.progressData = {
      labels: ['4 Minggu Lalu', '3 Minggu Lalu', '2 Minggu Lalu', 'Minggu Lalu', 'Minggu Ini'],
      scores: [75, 78, 77, 80, 82]
    };

    // Main recommendation
    this.mainRecommendation = {
      icon: '🎵',
      title: '<PERSON><PERSON> dan <PERSON>',
      description: 'Fokus pada ritme *stress-timed* untuk alur bicara yang lebih natural.',
      buttonText: '<PERSON><PERSON>'
    };

    // Learning categories
    this.categories = [
      {
        id: 'vocal-map',
        icon: '👄',
        title: 'Peta Vokal Amerika',
        description: 'Kuasai bunyi vokal seperti pada \'cat\' dan \'cut\'.',
        href: '#'
      },
      {
        id: 'minimal-pairs',
        icon: '↔️',
        title: 'Pasangan Minimal',
        description: 'Bedakan bunyi seperti \'ship\' vs \'sheep\'.',
        href: '#'
      },
      {
        id: 'consonant-clusters',
        icon: '🔡',
        title: 'Gugus Konsonan',
        description: 'Ucapkan kata seperti \'strengths\' dan \'world\'.',
        href: '#'
      },
      {
        id: 'paragraph-reading',
        icon: '📖',
        title: 'Membaca Paragraf',
        description: 'Latih kelancaran dan intonasi dalam konteks.',
        href: '#'
      },
      {
        id: 'real-world-scenarios',
        icon: '💬',
        title: 'Skenario Dunia Nyata',
        description: 'Simulasi percakapan sehari-hari.',
        href: '#'
      },
      {
        id: 'intensive-practice',
        icon: '🔥',
        title: 'Latihan Intensif',
        description: 'Tantang diri Anda dengan kalimat acak.',
        href: '#'
      }
    ];

    // UI Text
    this.uiText = {
      appTitle: 'AureaVoice',
      welcomeTitle: 'Selamat datang kembali, Budi!',
      welcomeSubtitle: 'Teruslah berlatih, konsistensi adalah kunci untuk mencapai aksen yang natural.',
      mainRecommendationTitle: 'Rekomendasi Latihan Utama Untuk Anda',
      progressChartTitle: 'Perkembangan Skor Anda',
      userScoreTitle: 'Skor Aksen Anda',
      userStatsTitle: 'Statistik Budi',
      categoriesTitle: 'Pilih Kategori Latihan Lainnya',
      logoutButton: 'Keluar',
      scoreImprovement: 'meningkat 2% dari minggu lalu!',
      statLabels: {
        completedExercises: 'Latihan Selesai',
        practiceTime: 'Waktu Latihan',
        categoriesTried: 'Kategori Dicoba',
        categoriesMastered: 'Kategori Dikuasai'
      }
    };
  }

  getUserData() {
    return this.userData;
  }

  getProgressData() {
    return this.progressData;
  }

  getMainRecommendation() {
    return this.mainRecommendation;
  }

  getCategories() {
    return this.categories;
  }

  getUIText() {
    return this.uiText;
  }

  // Method to get chart configuration for Chart.js
  getChartConfig() {
    const brandColors = {
      brightBlue: '#0079FF',
      deepBlue: '#004AAD',
    };

    return {
      type: 'line',
      data: {
        labels: this.progressData.labels,
        datasets: [{
          label: 'Skor Aksen',
          data: this.progressData.scores,
          backgroundColor: 'rgba(0, 121, 255, 0.1)',
          borderColor: brandColors.brightBlue,
          tension: 0.4,
          fill: true,
          pointBackgroundColor: brandColors.deepBlue,
          pointRadius: 5,
          pointHoverRadius: 7
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: false,
            min: 60,
            max: 100,
            ticks: {
              callback: function(value) {
                return value + '%';
              }
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              title: function(tooltipItems) {
                const item = tooltipItems[0];
                let label = item.chart.data.labels[item.dataIndex];
                return Array.isArray(label) ? label.join(' ') : label;
              }
            }
          }
        }
      }
    };
  }
}

export default LearningModel;
