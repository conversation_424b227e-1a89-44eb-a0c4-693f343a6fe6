import LearningView from '../views/LearningView.js';
import LearningModel from '../models/LearningModel.js';

class LearningPresenter {
  constructor(appModel) {
    this.view = null;
    this.appModel = appModel;
    this.learningModel = new LearningModel();
  }

  init() {
    // Prepare data for the view
    const viewData = this.prepareViewData();
    
    // Create and render the view
    this.view = new LearningView(viewData);
    this.render();
    
    // Set up event listeners
    this.setupEventListeners();
  }

  prepareViewData() {
    return {
      userData: this.learningModel.getUserData(),
      progressData: this.learningModel.getProgressData(),
      mainRecommendation: this.learningModel.getMainRecommendation(),
      categories: this.learningModel.getCategories(),
      uiText: this.learningModel.getUIText(),
      chartConfig: this.learningModel.getChartConfig()
    };
  }

  render() {
    const appElement = document.getElementById('app');
    appElement.innerHTML = '';

    const viewElement = this.view.render();
    appElement.appendChild(viewElement);
  }

  setupEventListeners() {
    // Handle logout button click
    const logoutButton = document.querySelector('.learning-nav-logout');
    if (logoutButton) {
      logoutButton.addEventListener('click', (e) => {
        e.preventDefault();
        this.handleLogout();
      });
    }

    // Handle recommendation button click
    const recommendationButton = document.querySelector('.recommendation-button');
    if (recommendationButton) {
      recommendationButton.addEventListener('click', () => {
        this.handleRecommendationClick();
      });
    }

    // Handle category card clicks
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach(card => {
      card.addEventListener('click', (e) => {
        e.preventDefault();
        const categoryId = this.getCategoryIdFromCard(card);
        this.handleCategoryClick(categoryId);
      });
    });
  }

  getCategoryIdFromCard(card) {
    // Extract category ID from the card element
    const title = card.querySelector('.category-title').textContent;
    const categories = this.learningModel.getCategories();
    const category = categories.find(cat => cat.title === title);
    return category ? category.id : null;
  }

  handleLogout() {
    // For now, navigate back to welcome page
    // In a real app, this would handle authentication logout
    console.log('Logout clicked - navigating to welcome page');
    window.location.hash = '#/';
  }

  handleRecommendationClick() {
    // Handle main recommendation button click
    console.log('Main recommendation clicked');
    // In a real app, this would navigate to the specific exercise
    alert('Fitur latihan akan segera tersedia!');
  }

  handleCategoryClick(categoryId) {
    // Handle category card click
    console.log(`Category clicked: ${categoryId}`);
    // In a real app, this would navigate to the specific category
    alert(`Kategori ${categoryId} akan segera tersedia!`);
  }

  destroy() {
    if (this.view) {
      this.view.destroy();
      this.view = null;
    }
  }
}

export default LearningPresenter;
